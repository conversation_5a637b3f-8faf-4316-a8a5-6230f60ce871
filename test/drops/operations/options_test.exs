defmodule Drops.Operations.OptionsTest do
  use Drops.OperationCase, async: false

  describe "options merging" do
    test "merges parent and child options correctly" do
      defmodule Test.ParentOp do
        use Drops.Operations, type: :command, repo: Drops.TestRepo
      end

      defmodule Test.ChildOp do
        use Test.ParentOp, type: :form

        @impl true
        def execute(_context) do
          {:ok,
           %{
             parent_opts: __MODULE__.__opts__(),
             operation_type: __MODULE__.__operation_type__()
           }}
        end
      end

      {:ok, result} = Test.ChildOp.call(%{params: %{}})

      # Child should inherit repo from parent but override type
      assert Keyword.get(result.parent_opts, :repo) == Drops.TestRepo
      assert result.operation_type == :form
    end

    test "child options override parent options" do
      defmodule Test.ParentWithDefaults do
        use Drops.Operations, type: :command, custom_option: :parent_value
      end

      defmodule Test.ChildOverride do
        use Test.ParentWithDefaults, custom_option: :child_value

        @impl true
        def execute(_context) do
          {:ok, %{opts: __MODULE__.__opts__()}}
        end
      end

      {:ok, result} = Test.ChildOverride.call(%{params: %{}})

      # Child should override parent's custom_option
      assert Keyword.get(result.opts, :custom_option) == :child_value
    end
  end

  describe "extension enabling/disabling" do
    @tag ecto_schemas: []
    operation [] do
      @impl true
      def execute(_context) do
        {:ok,
         %{
           enabled_extensions: enabled_extensions(),
           registered_extensions: registered_extensions(),
           opts: __opts__()
         }}
      end
    end

    test "Ecto extension is enabled when repo is provided", %{operation: operation} do
      {:ok, result} = operation.call(%{params: %{}})

      # Ecto extension should be enabled
      assert Drops.Operations.Extensions.Ecto in result.enabled_extensions
    end

    operation [] do
      @impl true
      def execute(_context) do
        {:ok,
         %{
           enabled_extensions: enabled_extensions(),
           registered_extensions: registered_extensions(),
           opts: __opts__()
         }}
      end
    end

    test "Ecto extension is disabled when repo is not provided", %{operation: operation} do
      {:ok, result} = operation.call(%{params: %{}})

      # Ecto extension should not be enabled
      refute Drops.Operations.Extensions.Ecto in result.enabled_extensions
    end
  end

  describe "schema options handling" do
    operation type: :form do
      @impl true
      def execute(%{params: params}) do
        # If atomize: true is working, keys should be atoms
        {:ok, %{has_atom_keys: is_atom(Map.keys(params) |> List.first())}}
      end
    end

    test "form operations get atomize: true by default", %{operation: operation} do
      {:ok, result} = operation.call(%{params: %{"name" => "Jane"}})
      assert result.has_atom_keys == true
    end

    operation type: :command do
      @impl true
      def execute(%{params: params}) do
        # If atomize is not set, keys should remain as strings
        {:ok, %{has_string_keys: is_binary(Map.keys(params) |> List.first())}}
      end
    end

    test "command operations don't get atomize by default", %{operation: operation} do
      {:ok, result} = operation.call(%{params: %{"name" => "Jane"}})
      assert result.has_string_keys == true
    end

    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    operation do
      schema(Test.Ecto.TestSchemas.UserSchema)

      @impl true
      def execute(%{changeset: changeset}) do
        # If cast: true is working, we should get a changeset
        {:ok, %{has_changeset: match?(%Ecto.Changeset{}, changeset)}}
      end
    end

    test "Ecto extension provides cast: true by default", %{operation: operation} do
      {:ok, result} =
        operation.call(%{params: %{name: "Jane", email: "<EMAIL>"}})

      assert result.has_changeset == true
    end
  end

  describe "user overrides" do
    @tag ecto_schemas: [Test.Ecto.TestSchemas.UserSchema]
    operation schema: [cast: false] do
      schema(Test.Ecto.TestSchemas.UserSchema)

      @impl true
      def execute(%{params: params}) do
        # If cast: false override works, we should get params not changeset
        {:ok, %{has_params: Map.has_key?(params, "name")}}
      end
    end

    test "user can override extension schema defaults", %{operation: operation} do
      {:ok, result} =
        operation.call(%{params: %{"name" => "Jane", "email" => "<EMAIL>"}})

      assert result.has_params == true
    end

    operation type: :form, schema: [atomize: false] do
      schema do
        %{
          required("name") => string()
        }
      end

      @impl true
      def execute(%{params: params}) do
        # If atomize: false override works, keys should remain as strings
        {:ok, %{has_string_keys: is_binary(Map.keys(params) |> List.first())}}
      end
    end

    test "user can override form operation defaults", %{operation: operation} do
      {:ok, result} = operation.call(%{params: %{"name" => "Jane"}})
      assert result.has_string_keys == true
    end
  end

  describe "options inheritance" do
    test "grandchild inherits from grandparent through parent" do
      defmodule Test.GrandParent do
        use Drops.Operations, custom_value: :grandparent, repo: Drops.TestRepo
      end

      defmodule Test.Parent do
        use Test.GrandParent, custom_value: :parent
      end

      defmodule Test.GrandChild do
        use Test.Parent, type: :form

        @impl true
        def execute(_context) do
          {:ok, %{opts: __MODULE__.__opts__()}}
        end
      end

      {:ok, result} = Test.GrandChild.call(%{params: %{}})

      # Should inherit repo from grandparent, custom_value from parent, and set own type
      assert Keyword.get(result.opts, :repo) == Drops.TestRepo
      assert Keyword.get(result.opts, :custom_value) == :parent
      assert Keyword.get(result.opts, :type) == :form
    end
  end
end
